'use client';

import AdminDashboardLayout from '@/components/navigation/AdminDashboardLayout';
import withAdminAuth from '@/components/withAdminAuth';

interface AdminSettingsProps {
  adminData: any;
}

function AdminSettingsPage({ adminData }: AdminSettingsProps) {
  const userName = adminData ? `${adminData.first_name || ''} ${adminData.last_name || ''}`.trim() || adminData.username || 'Admin' : 'Admin';

  return (
    <AdminDashboardLayout activePage="settings" userName={userName}>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">Admin Settings</h1>
        <p className="text-gray-600 mt-2">Manage your notification preferences and account settings</p>
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <p>Settings content will be implemented here</p>
        </div>
      </div>
    </AdminDashboardLayout>
  );
}

export default withAdminAuth(AdminSettingsPage);
