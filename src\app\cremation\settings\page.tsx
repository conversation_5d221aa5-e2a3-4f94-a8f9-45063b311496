'use client';

import CremationDashboardLayout from '@/components/navigation/CremationDashboardLayout';
import withBusinessVerification from '@/components/withBusinessVerification';

interface CremationSettingsProps {
  userData: any;
}

function CremationSettingsPage({ userData }: CremationSettingsProps) {
  return (
    <CremationDashboardLayout activePage="settings" userData={userData}>
      <div className="p-6">
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="text-gray-600 mt-2">Manage your notification preferences and account settings</p>
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <p>Settings content will be implemented here</p>
        </div>
      </div>
    </CremationDashboardLayout>
  );
}

export default withBusinessVerification(CremationSettingsPage);
